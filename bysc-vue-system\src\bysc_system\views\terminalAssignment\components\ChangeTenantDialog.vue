<template>
  <el-drawer
    title="更改租户"
    :visible.sync="drawerVisible"
    direction="rtl"
    size="500px"
    :close-on-press-escape="false"
    :modal-append-to-body="false"
    @close="handleClose"
  >
    <el-form :model="form" :rules="rules" ref="changeForm" label-width="100px">
      <el-form-item label="证件柜ID">
        <el-input v-model="form.icbId" disabled></el-input>
      </el-form-item>
      <el-form-item label="证件柜名称">
        <el-input v-model="form.icbName" disabled></el-input>
      </el-form-item>
      <el-form-item label="当前租户">
        <el-input v-model="form.currentTenant" disabled></el-input>
      </el-form-item>
      <el-form-item label="选择租户" prop="tenantId">
        <el-select
          v-model="form.tenantId"
          placeholder="支持搜索，不能选择当前租户"
          style="width: 100%"
          filterable
        >
          <el-option
            v-for="tenant in availableTenants"
            :key="tenant.id"
            :label="tenant.tenantName"
            :value="tenant.id">
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 注意提示 -->
      <el-alert
        title="注意：更改租户后，该设备本地数据（用户、班组、策略）将会被清空，需要到新租户上进行重新配置操作！"
        type="warning"
        :closable="false"
        show-icon
        style="margin-bottom: 20px;">
      </el-alert>
    </el-form>

    <div class="drawer-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">确 定</el-button>
    </div>

    <!-- 再次确认弹窗 -->
    <el-dialog
      title="再次确认更改"
      :visible.sync="confirmDialogVisible"
      width="400px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div style="text-align: center; padding: 20px 0;">
        <p style="font-size: 16px; color: #E6A23C; margin-bottom: 20px;">
          请再次确认是否要进行租户绑定
        </p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="confirmDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleFinalConfirm" :loading="loading">确 定</el-button>
      </div>
    </el-dialog>
  </el-drawer>
</template>

<script>
export default {
  name: 'ChangeTenantDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    terminalInfo: {
      type: Object,
      default: () => ({})
    },
    tenantList: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        icbId: '',
        icbName: '',
        currentTenant: '',
        tenantId: ''
      },
      rules: {
        tenantId: [
          {required: true, message: '请选择租户', trigger: 'change'}
        ]
      },
      confirmDialogVisible: false
    };
  },
  computed: {
    drawerVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    },
    // 过滤掉当前租户，不能选择当前租户
    availableTenants() {
      return this.tenantList.filter(tenant =>
        tenant.id !== this.terminalInfo.distributeTenantId
      );
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm();
      } else {
        this.confirmDialogVisible = false;
      }
    },
    terminalInfo: {
      handler(val) {
        if (val && this.visible) {
          this.initForm();
        }
      },
      deep: true
    }
  },
  methods: {
    initForm() {
      this.form = {
        icbId: this.terminalInfo.icbId || '',
        icbName: this.terminalInfo.icbName || '',
        currentTenant: this.terminalInfo.currentTenant || '',
        tenantId: ''
      };
      // 清除验证
      this.$nextTick(() => {
        if (this.$refs.changeForm) {
          this.$refs.changeForm.clearValidate();
        }
      });
    },

    handleConfirm() {
      this.$refs.changeForm.validate(valid => {
        if (valid) {
          // 显示再次确认弹窗
          this.confirmDialogVisible = true;
        }
      });
    },

    handleFinalConfirm() {
      const data = {
        icbId: this.form.icbId,
        tenantId: this.form.tenantId
      };
      this.confirmDialogVisible = false;
      this.$emit('confirm', data);
    },

    handleCancel() {
      this.drawerVisible = false;
    },

    handleClose() {
      this.form = {
        icbId: '',
        icbName: '',
        currentTenant: '',
        tenantId: ''
      };
      this.confirmDialogVisible = false;
      if (this.$refs.changeForm) {
        this.$refs.changeForm.clearValidate();
      }
      this.$emit('close');
    }
  }
};
</script>

<style lang="less" scoped>
.drawer-footer {
  text-align: right;
  padding: 20px;
  border-top: 1px solid #e8e8e8;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
}

.dialog-footer {
  text-align: right;
}

::v-deep .el-drawer__body {
  padding-bottom: 80px;
}

::v-deep .el-alert__content {
  line-height: 1.5;
}
</style>
