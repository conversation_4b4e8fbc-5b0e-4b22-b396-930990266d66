
<template>
  <el-drawer
    title="维护管理员"
    :visible.sync="dialogVisible"
    direction="rtl"
    size="80%"
    :close-on-press-escape="false"
    :wrapperClosable="false"
    @close="handleClose"
  >
    <div class="admin-manage-container">
      <!-- 操作按钮区域 -->
      <div class="action-bar">
        <el-button type="primary" size="small" @click="handleAdd">添加</el-button>
      </div>

      <!-- 管理员列表表格 -->
      <el-table
        :data="currentPageData"
        stripe
        style="width: 100%"
        v-loading="tableLoading"
        height="400"
        border
      >
        <el-table-column
          prop="account"
          label="用户名"
          width="150"
          align="center"
        />
        <el-table-column
          prop="nickname"
          label="昵称"
          width="150"
          align="center"
        />
        <el-table-column
          prop="mobile"
          label="电话"
          width="150"
          align="center"
        />
        <el-table-column
          label="角色"
          width="150"
          align="center"
        >
          <template>
            租户管理员
          </template>
        </el-table-column>
        <el-table-column
          prop="organizationName"
          label="组织"
          min-width="200"
          align="center"
        />
        <el-table-column
          label="操作"
          width="250"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              style="margin-right: 10px;"
              @click="handleEdit(scope.row)"
            >
              修改
            </el-button>
            <el-popconfirm
              title="确定要删除该管理员吗？"
              @confirm="handleDelete(scope.row)"
            >
              <el-button
                type="text"
                size="small"
                style="color: #f56c6c"
                slot="reference"
              >
                删除
              </el-button>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        />
      </div>
    </div>

    <!-- 添加/编辑管理员表单弹窗 -->
    <el-dialog
      :title="formTitle"
      :visible.sync="formDialogVisible"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form
        :model="adminForm"
        :rules="formRules"
        ref="adminForm"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="account">
          <el-input
            v-model="adminForm.account"
            placeholder="请输入用户名"
            :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input
            v-model="adminForm.nickname"
            placeholder="请输入昵称"
          />
        </el-form-item>


        <!-- 添加模式下显示的字段 -->
        <template v-if="!isEdit">

            <el-form-item label="电话" prop="mobile">
          <el-input
            v-model="adminForm.mobile"
            placeholder="请输入电话"
            maxlength="11"

          />
        </el-form-item>

          <el-form-item label="密码" prop="password">
            <el-input
              v-model="adminForm.password"
              type="password"
              placeholder="请输入密码"
              show-password
            />
          </el-form-item>
          <el-form-item label="再次确认密码" prop="confirmPassword">
            <el-input
              v-model="adminForm.confirmPassword"
              type="password"
              placeholder="请再次输入密码"
              show-password
            />
          </el-form-item>
          <el-form-item label="角色" prop="roleId">
            <el-select
              v-model="adminForm.roleId"
              placeholder="请选择角色"
              style="width: 100%"
              disabled
            >
              <el-option
                v-for="role in roleList"
                :key="role.id"
                :label="role.name"
                :value="role.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="组织" prop="organizationId">
            <el-select
              v-model="adminForm.organizationId"
              placeholder="请选择组织"
              style="width: 100%"
              disabled
            >
              <el-option
                v-for="org in orgList"
                :key="org.id"
                :label="org.name"
                :value="org.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleFormCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleFormSubmit"
          :loading="formLoading"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
  </el-drawer>
</template>

<script>
export default {
  name: 'AdminManageDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    tenantInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      formDialogVisible: false,
      tableLoading: false,
      formLoading: false,
      isEdit: false,
      formTitle: '添加管理员',
      // 分页相关数据
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      adminList: [],
      roleList: [],
      orgList: [
      ],
      adminForm: {
        id: null,
        account: '',
        nickname: '',
        mobile: '',
        password: '',
        confirmPassword: '',
        roleId: null,
        organizationId: null,
        tenantId: null,
        organizationName: ''
      },
      formRules: {
        account: [
          {required: true, message: '请输入用户名', trigger: 'blur'},
          {
            min: 3,
            max: 16,
            message: '账号长度为 3 到 16 个字符',
            trigger: 'blur'
          },
          {
            pattern: /^[a-z0-9_-]+$/,
            message: '账号格式不正确，只能包含小写字母、数字、下划线 _、连字符 -',
            trigger: 'blur'
          }
        ],
        nickname: [
          {required: true, message: '请输入昵称', trigger: 'blur'}
        ],
        mobile: [
          {required: true, message: '请输入电话', trigger: 'blur'},
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的电话格式',
            trigger: 'blur'
          }
        ],
        password: [
          {required: true, message: '请输入密码', trigger: 'blur'},
          {min: 6, message: '密码长度不能少于6位', trigger: 'blur'}
        ],
        confirmPassword: [
          {required: true, message: '请再次输入密码', trigger: 'blur'},
          {
            validator: (rule, value, callback) => {
              if (value !== this.adminForm.password) {
                callback(new Error('两次输入的密码不一致'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        roleId: [
          {required: true, message: '请选择角色', trigger: 'change'}
        ],
        organizationId: [
          {required: true, message: '请选择组织', trigger: 'change'}
        ]
      }
    };
  },
  computed: {
    // 当前页显示的数据（现在直接返回adminList，因为后端已经分页）
    currentPageData() {
      return this.adminList;
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.loadAdminList();
        this.loadRoleList();
        this.loadOrgList();
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
    }
  },
  methods: {
    // 加载管理员列表
    loadAdminList() {
      if (!this.tenantInfo.id) {
        return;
      }

      this.tableLoading = true;
      // 调用真实API接口
      this.$api['tenant/baseTenantAdmin-page']({
        current: this.pagination.currentPage,
        limit: this.pagination.pageSize,
        param: {
          tenantId: this.tenantInfo.id
        }
      }).then(res => {
        if (res) {
          this.adminList = res.list || [];
          this.pagination.total = res.total || 0;
        }
      }).catch(error => {
        console.error('加载管理员列表失败:', error);
        this.$message.error('加载管理员列表失败');
        this.adminList = [];
        this.pagination.total = 0;
      }).finally(() => {
        this.tableLoading = false;
      });
    },

    // 加载角色列表
    loadRoleList() {
      // 设置固定的租户管理员角色
      this.roleList = [
        {
          id: 'tenant_admin',
          name: '租户管理员',
          code: 'TENANT_ADMIN'
        }
      ];
      console.log('角色列表已加载:', this.roleList);
    },

    // 加载组织列表
    loadOrgList() {
      // 使用来自 tenantInfo 的组织信息
      if (this.tenantInfo.organizationName && this.tenantInfo.organizationId) {
        this.orgList = [
          {
            id: this.tenantInfo.organizationId,
            name: this.tenantInfo.organizationName,
            code: 'tenant_org'
          }
        ];
      }
      console.log('组织列表已加载:', this.orgList);
    },

    // 添加管理员
    handleAdd() {
      this.isEdit = false;
      this.formTitle = '添加管理员';

      // 设置默认的角色和组织
      const defaultRoleId = this.roleList.length > 0 ? this.roleList[0].id : null;
      const defaultorganizationId = this.orgList.length > 0 ? this.orgList[0].id : null;
      const defaultOrgName = this.orgList.length > 0 ? this.orgList[0].name : '';

      this.adminForm = {
        id: null,
        account: '',
        nickname: '',
        mobile: '',
        password: '',
        confirmPassword: '',
        roleId: defaultRoleId, // 默认选择租户管理员
        organizationId: this.tenantInfo.organizationId || defaultorganizationId,
        tenantId: this.tenantInfo.id,
        organizationName: this.tenantInfo.organizationName || defaultOrgName
      };
      this.formDialogVisible = true;
    },

    // 编辑管理员
    handleEdit(row) {
      this.isEdit = true;
      this.formTitle = '编辑管理员';
      this.adminForm = {
        id: row.id,
        account: row.account,
        nickname: row.nickname,
        mobile: row.mobile || '',
        password: '', // 编辑时不显示密码字段
        confirmPassword: '',
        roleId: row.roleId,
        organizationId: row.organizationId || this.tenantInfo.organizationId || null,
        tenantId: this.tenantInfo.id,
        organizationName: row.organizationName || this.tenantInfo.organizationName || ''
      };
      this.formDialogVisible = true;
    },

    // 删除管理员
    handleDelete(row) {
      // 调用真实API接口删除
      this.$api['tenant/baseTenantAdmin-delete']({
        id: row.id
      }).then(() => {
        this.$message.success('删除成功');
        // 重新加载列表
        this.loadAdminList();
      }).catch(error => {
        console.error('删除管理员失败:', error);
        this.$message.error(error);
      });
    },

    // 表单提交
    handleFormSubmit() {
      this.$refs.adminForm.validate(valid => {
        if (valid) {
          this.formLoading = true;

          // 准备提交的数据，确保包含 organizationName 和 organizationId
          const submitData = {
            ...this.adminForm,
            organizationName: this.adminForm.organizationName || this.tenantInfo.organizationName || '',
            organizationId: this.adminForm.organizationId || this.tenantInfo.organizationId || null
          };

          console.log('提交的管理员数据:', submitData);

          // 调用真实API接口保存
          this.$api['tenant/baseTenantAdmin-save'](submitData).then(() => {
            this.$message.success(this.isEdit ? '修改成功' : '添加成功');
            this.formDialogVisible = false;
            // 重新加载列表
            this.loadAdminList();
          }).catch(error => {
            console.error('保存管理员失败:', error);
            this.$message.error('保存失败');
          }).finally(() => {
            this.formLoading = false;
          });
        }
      });
    },

    // 表单取消
    handleFormCancel() {
      this.formDialogVisible = false;
      this.$refs.adminForm.resetFields();
    },

    // 关闭主弹窗
    handleClose() {
      this.dialogVisible = false;
    },

    // 分页相关方法
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.pagination.currentPage = 1; // 重置到第一页
      this.loadAdminList(); // 重新加载数据
    },

    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.loadAdminList(); // 重新加载数据
    }
  }
};
</script>

<style lang="less" scoped>
.admin-manage-container {
  padding: 20px;
  height: calc(100vh - 120px); // 最大化利用屏幕高度
  display: flex;
  flex-direction: column;

  .action-bar {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebeef5;
    flex-shrink: 0; // 不缩放
  }

  .el-table {
    border: 1px solid #ebeef5;
    flex: 1; // 占据剩余空间
    overflow: auto;
  }

  .pagination-container {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
    text-align: right;
    flex-shrink: 0; // 不缩放
  }
}

.dialog-footer {
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

// 抽屉样式优化
:deep(.el-drawer) {
  .el-drawer__header {
    padding: 20px 20px 0 20px;
    margin-bottom: 0;
  }

  .el-drawer__body {
    padding: 0;
    overflow-y: auto;
  }
}
</style>
