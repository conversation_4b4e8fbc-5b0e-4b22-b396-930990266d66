export default [
  {
    name: 'tenant-page', // 租户分页
    method: 'POST',
    path: '/base/tenant/page'
  },

  {
    name: 'tenant-list', // 租户列表
    method: 'POST',
    path: '/base/tenant/findListByIsSelect'
  },
  {
    name: 'switch-tenantstatus',
    method: 'POST',
    path: '/base/tenant/switch-status'
  },
  {
    name: 'tenant-save', // 保存
    method: 'POST',
    path: '/base/tenant/save'
  },
  {
    name: 'tenant-delete', // 删除
    method: 'POST',
    path: '/base/tenant/delete'
  },
  {
    name: 'baseTenantAdmin-page', // 租户管理员表分页
    method: 'POST',
    path: '/base/baseTenantAdmin/page'
  },

  {
    name: 'baseTenantAdmin-save', // 保存租户管理员表
    method: 'POST',
    path: '/base/baseTenantAdmin/save'
  },
  {
    name: 'baseTenantAdmin-delete', // 删除
    method: 'OTHER',
    path: '/base/baseTenantAdmin/delete'
  }
];
